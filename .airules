#  Riva Ash - AI Agent Guidelines

**Version**: 1.0
**Last Updated**: 2024-08-01

## Project Overview

**Riva** is a comprehensive business management system built with Elixir/Phoenix and Ash Framework, featuring reservation management, employee permissions, and real-time capabilities. The project follows a packages-based architecture with all code organized under the `packages/` directory.

### Key Technologies
- **Backend**: Elixir 1.19+, Phoenix 1.7+, Ash Framework 3.5+
- **Database**: PostgreSQL with UUID primary keys
- **Frontend**: LiveView with React integration (live_react)
- **Testing**: ExUnit with property-based testing (StreamData)
- **Authentication**: AshAuthentication with role-based access
- **Authorization**: Ash Policies with SimpleSat SAT solver
- **UI**: Tailwind CSS with Atomic Design patterns

## Project Structure

```
packages/
├── riva_ash/                 # Main Ash application
│   ├── lib/
│   │   ├── riva_ash/
│   │   │   ├── resources/    # Ash resources (Business, Item, etc.)
│   │   │   ├── reactors/     # Complex business logic workflows
│   │   │   ├── policies/     # Authorization policies
│   │   │   ├── permissions/  # Permission system
│   │   │   └── validations/  # Custom validations
│   │   └── riva_ash_web/
│   │       ├── components/   # UI components (atomic design)
│   │       ├── live/         # LiveView pages
│   │       └── controllers/  # Phoenix controllers
│   ├── test/                 # Test files
│   └── priv/                 # Migrations, seeds
└── test/                     # Shared test utilities
```

## Development Guidelines

### 1. Resource Development

**Standard Extensions**: Every resource must include:
- AshPaperTrail (audit trails)
- AshArchival (soft delete)
- Proper policies with admin bypass
- UUID primary keys
- Timestamps (inserted_at, updated_at)

**Relationships**: Use proper Ash relationships with foreign key constraints.

### 2. Database Patterns

**Soft Delete**: Use AshArchival for all resources requiring deletion.

**Audit Trails**: AshPaperTrail tracks all changes.

**Grid Positioning**: Use row/column grid system instead of x,y coordinates for layouts.

### 3. Business Logic Patterns

**Reservation System**:
- Full-day billing only
- No weekend/weekday differentiation
- Constant pricing with business exceptions
- Row/column positioning for items

**Permission Hierarchy**:
- Admin: Full system access
- Business Owner: Full business access
- Employee: Permission-based access
- Client: Limited self-service access

## Common Patterns

### 1. Creating New Resources

1. Define in `lib/riva_ash/resources/`
2. Include all standard extensions
3. Add to domain
4. Create migration
5. Add policies with admin bypass
6. **MANDATORY**: Write comprehensive test suite:
   - Property-based tests for all actions (create, read, update, destroy)
   - Policy tests with various user roles and permissions
   - Validation tests with random invalid data
   - Relationship tests with associated resources
   - Archive/soft delete functionality tests

### 2. Adding Complex Workflows

1. Create Reactor in `lib/riva_ash/reactors/`
2. Define clear inputs/outputs
3. Include compensation logic
4. Add to resource as custom action if needed
5. **MANDATORY**: Comprehensive test suite:
   - Property-based tests with random valid input combinations
   - Error scenario tests with invalid inputs
   - Compensation logic tests (rollback scenarios)
   - Integration tests with all affected resources
   - Performance tests for complex workflows
   - Edge case tests with boundary conditions

### 3. UI Component Creation

1. Follow atomic design hierarchy
2. Create in appropriate component directory
3. Add to Storybook
4. **MANDATORY**: Comprehensive test suite:
   - Property-based tests for component props with random valid values
   - Interaction tests using `phoenix_test`
   - Accessibility tests for proper ARIA attributes
   - Responsive design tests across different screen sizes
   - Form validation tests with random invalid inputs
   - LiveView event handling tests
5. Document props and usage in Storybook

## Git Workflow

**Commit Author**: All commits should use `<EMAIL>`

**App Name**: Use "Riva" in all user-facing text and documentation.

**Repository**: Private GitHub at `https://github.com/albin-mema/Riva_elixir.git`

## Development Environment

**Shell**: Fish shell (not Bash)

**Docker**: Docker Desktop available for database connections

**Package Manager**: Use appropriate package managers (mix, npm/pnpm) - never edit package files directly

## Key Resources

- Business, Employee, Client, Item, Section, Plot, Layout
- Reservation, Payment, Pricing, Permission
- ItemPosition, ItemHold, ItemSchedule, AvailabilityException
- RecurringReservation, RecurringReservationInstance

## Testing Commands

```fish
# Run all tests
mix test

# Run property tests specifically
./run-property-tests.sh

# Run tests with property test statistics
mix test --include property

# Run specific test file
mix test test/path/to/test.exs

# Run tests with coverage
mix test --cover

# Run tests in watch mode during development
mix test.watch
```

## Important Notes

- **MANDATORY TESTING**: Every piece of generated code MUST include comprehensive tests
- **Property-based testing REQUIRED**: Use StreamData for all tests where applicable
- **Test before considering code complete**: Code without tests is incomplete
- **Never bypass authentication** in production code
- **Always use Reactor** for complex multi-step business logic
- **Centralize permissions** in Constants module
- **Follow atomic design** for all UI components
- **Prefer LiveView** over React unless specific needs require it

## Code Quality Standards

### 1. Error Handling
- Use Ash's built-in error handling patterns
- Implement proper error boundaries in LiveView
- Return meaningful error messages to users
- Log errors appropriately for debugging

### 2. Performance Considerations
- Use Ash's built-in query optimization
- Implement proper pagination with Flop
- Avoid N+1 queries through proper loading
- Use database indexes for frequently queried fields

### 3. Security Best Practices
- Always validate user input
- Use Ash policies for authorization
- Sanitize data before display
- Follow OWASP guidelines for web security

## Debugging and Development Tools

### 1. Available Tools
- **AshAdmin**: Web-based admin interface at `/admin`
- **GraphQL Playground**: Available for API exploration
- **LiveView Debugger**: Use for LiveView debugging
- **Ash Console**: `iex -S mix` for interactive development

### 2. Common Debugging Commands
```elixir
# Inspect Ash queries
Ash.Query.to_sql(query, RivaAsh.Repo)

# Debug policies
Ash.Policy.Info.policies(Resource)

# Check resource info
Ash.Resource.Info.actions(Resource)
```

## API Design Patterns

### 1. JSON API
- All resources exposed via AshJsonApi
- Follow JSON:API specification
- Use proper HTTP status codes
- Include relationship links

### 2. GraphQL
- Available for complex queries
- Use for frontend data fetching
- Implement proper field selection
- Handle errors gracefully

## Deployment Considerations

### 1. Environment Configuration
- Use environment variables for secrets
- Configure different environments properly
- Set up proper logging levels
- Configure database connections

### 2. Database Management
- Run migrations in order
- Use seeds for initial data
- Backup strategies for production
- Monitor database performance

## Troubleshooting Common Issues

### 1. Ash Policy Errors
- Check policy definitions in resources
- Verify actor is properly set
- Use policy breakdowns for debugging
- Ensure permissions exist in Constants

### 2. LiveView Issues
- Check socket connections
- Verify proper assigns usage
- Debug with LiveView debugger
- Test with different browsers

### 3. Database Issues
- Check migration status
- Verify foreign key constraints
- Monitor query performance
- Check connection pool settings

## Contributing Guidelines

### 1. Code Review Checklist
- [ ] Tests pass and cover new functionality
- [ ] Documentation updated
- [ ] Follows established patterns
- [ ] Security considerations addressed
- [ ] Performance impact assessed

### 2. Pull Request Guidelines
- Clear description of changes
- Reference related issues
- Include test coverage
- Update documentation as needed
- Follow commit message conventions

## Resources and Documentation

### 1. Key Documentation
- Ash Framework: https://ash-hq.org/
- Phoenix LiveView: https://hexdocs.pm/phoenix_live_view/
- Elixir: https://elixir-lang.org/docs.html
- PostgreSQL: https://www.postgresql.org/docs/

### 2. Context-Specific Documentation

**For New Contributors:**
- Start with [Contributing Guide](./documentation/CONTRIBUTING.md)
- Follow [Setup Guide](./documentation/SETUP_GUIDE.md) for environment setup
- Review [Development Workflow](./documentation/DEVELOPMENT_WORKFLOW.md)

**For Feature Development:**
- Reference [Architectural Patterns](./packages/riva_ash/patterns.md) for implementation guidance
- Use [Testing Guide](./packages/riva_ash/docs/testing_guide.md) for comprehensive testing
- Follow [UI Guidelines](./documentation/ui-guidelines.md) for component development

**For System Understanding:**
- Review [Architecture Guidelines](./documentation/architecture-guidelines.md)
- Check [Reservation System Assessment](./documentation/RESERVATION_SYSTEM_ASSESSMENT.md)
- Examine [User Actions](./documentation/USER_ACTIONS.md) specifications

**For Integration Work:**
- See [Timex Integration](./documentation/TIMEX_INTEGRATION.md) for time handling
- Check existing test files for usage examples
- Consult Storybook for UI component documentation

## Detailed Guidelines

For more detailed guidelines, please refer to the following documents based on your needs:

### Core Development
- [Architecture Guidelines](./documentation/architecture-guidelines.md) - Architectural patterns and design principles
- [Testing Guidelines](./documentation/testing-guidelines.md) - Comprehensive testing strategies and requirements
- [UI Guidelines](./documentation/ui-guidelines.md) - UI component development and atomic design patterns

### Getting Started
- [Setup Guide](./documentation/SETUP_GUIDE.md) - Complete environment setup with troubleshooting
- [Development Workflow](./documentation/DEVELOPMENT_WORKFLOW.md) - Development process and best practices
- [Contributing Guide](./documentation/CONTRIBUTING.md) - Comprehensive contribution guidelines

### Technical Deep Dive
- [Architectural Patterns](./packages/riva_ash/patterns.md) - Detailed implementation patterns with examples
- [Testing Guide](./packages/riva_ash/docs/testing_guide.md) - In-depth testing strategies and tools

## Example Implementations

For example implementations, please refer to the following files:

- **Resources**: `packages/riva_ash/lib/riva_ash/resources/`
- **Reactors**: `packages/riva_ash/lib/riva_ash/reactors/`
- **UI Components**: `packages/riva_ash/lib/riva_ash_web/components/`
- **Tests**: `packages/riva_ash/test/`

## AI Instructions

When generating code for this project, please follow these specific instructions:

1. **Always use Reactor** for complex multi-step business logic
2. **Centralize permissions** in the Constants module
3. **Follow atomic design** for all UI components
4. **Prefer LiveView** over React unless specific needs require it
5. **Include comprehensive tests** for all generated code
6. **Use property-based testing** with StreamData where applicable
7. **Follow the established patterns** for resources, reactors, and components

## Code Verification

To verify that generated code follows the project's patterns:

1. Check that resources include all standard extensions
2. Verify that complex workflows use Reactor
3. Ensure UI components follow atomic design
4. Confirm that permissions are centralized
5. Validate that tests are comprehensive and use property-based testing
6. Review that code follows the established patterns in existing implementations

## Error Handling Patterns

The project uses the following error handling patterns:

1. **Ash's built-in error handling**: Use Ash's error types and patterns
2. **LiveView error boundaries**: Implement proper error boundaries in LiveView components
3. **Meaningful error messages**: Return clear, user-friendly error messages
4. **Appropriate logging**: Log errors with sufficient context for debugging

## Performance Considerations

Performance considerations specific to the technologies used:

1. **Ash Query Optimization**: Use Ash's built-in query optimization features
2. **Pagination**: Implement proper pagination with Flop
3. **N+1 Prevention**: Avoid N+1 queries through proper loading
4. **Database Indexes**: Use database indexes for frequently queried fields
5. **Caching**: Implement caching strategies where appropriate
