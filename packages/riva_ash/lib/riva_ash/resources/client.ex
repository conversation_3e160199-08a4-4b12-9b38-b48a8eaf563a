defmodule RivaAsh.Resources.Client do
  @moduledoc """
  Represents a client who can make reservations.
  Clients can be either registered or unregistered.
  """

  use Ash.Resource,
    domain: RivaAsh.Domain,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [
      AshJsonApi.Resource,
      AshGraphql.Resource,
      AshPaperTrail.Resource,
      AshArchival.Resource,
      AshAdmin.Resource
    ]

  import RivaAsh.ResourceHelpers
  import RivaAsh.Authorization

  standard_postgres("clients")
  standard_archive()
  standard_paper_trail()

  policies do
    # Admins can do everything
    bypass actor_attribute_equals(:role, :admin) do
      authorize_if(always())
    end

    # Managers can read and manage clients
    policy actor_attribute_equals(:role, :manager) do
      authorize_if(always())
    end

    # Staff can only read clients (for reservation purposes)
    policy actor_attribute_equals(:role, :staff) do
      authorize_if(action_type(:read))
    end

    # Clients can only access their own data
    policy actor_attribute_equals(:__struct__, RivaAsh.Resources.Client) do
      authorize_if(expr(id == ^actor(:id)))
      # Clients can read and update their own data
      forbid_unless(action_type([:read, :update]))
    end

    # Secure client creation - only allow legitimate use cases
    policy action_type(:create) do
      # Public registration
      authorize_if(always())
    end

    # Business-scoped read access
    policy action_type(:read) do
      authorize_if(actor_attribute_equals(:id, :id) || action_has_permission(:manage_clients))
    end

    # Allow public read for booking lookups (by email) - but should be business-scoped
    policy action(:by_email) do
      # This should be restricted in production - consider requiring business context
      authorize_if(always())
    end

    # Regular users (business owners) can read clients
    policy action_type(:read) do
      authorize_if(actor_attribute_equals(:role, :user))
    end
  end

  json_api do
    type("client")

    routes do
      base("/clients")

      get(:read)
      index(:read)
      post(:create)
      patch(:update)
      delete(:destroy)

      # Additional routes for client-specific actions
      get(:by_email, route: "/by-email/:email")
      get(:registered, route: "/registered")
      get(:unregistered, route: "/unregistered")
    end
  end

  graphql do
    type(:client)

    queries do
      get(:get_client, :read)
      list(:list_clients, :read)
      get(:client_by_email, :by_email)
      list(:registered_clients, :registered)
      list(:unregistered_clients, :unregistered)
    end

    mutations do
      create(:create_client, :create)
      create(:create_client_for_booking, :create_for_booking)
      update(:update_client, :update)
      update(:register_client, :register)
      destroy(:delete_client, :destroy)
    end
  end

  code_interface do
    define(:create, action: :create)
    define(:create_for_booking, action: :create_for_booking)
    define(:read, action: :read)
    define(:update, action: :update)
    define(:destroy, action: :destroy)
    define(:register, action: :register)
    define(:by_id, args: [:id], action: :by_id)
    define(:by_email, args: [:email], action: :by_email)
    define(:by_business, args: [:business_id], action: :by_business)
    define(:by_business_and_email, args: [:business_id, :email], action: :by_business_and_email)
    define(:registered, action: :registered)
    define(:unregistered, action: :unregistered)
  end

  actions do
    defaults([:read, :update, :destroy])

    create :create do
      accept([:business_id, :name, :email, :phone, :is_registered])
      primary?(true)

      # Validate business access
      # validate(&RivaAsh.Validations.validate_business_access/2)

      # Ensure email is provided for registered clients
      validate(fn changeset, _ ->
        if Ash.Changeset.get_attribute(changeset, :is_registered) do
          case Ash.Changeset.get_attribute(changeset, :email) do
            nil -> {:error, field: :email, message: "is required for registered clients"}
            "" -> {:error, field: :email, message: "is required for registered clients"}
            _ -> :ok
          end
        else
          :ok
        end
      end)

      # Generate verification token for registered clients
      change(fn changeset, _ ->
        if Ash.Changeset.get_attribute(changeset, :is_registered) do
          token = generate_verification_token()

          changeset
          |> Ash.Changeset.force_change_attribute(:verification_token, token)
          |> Ash.Changeset.force_change_attribute(:email_verified, false)
        else
          changeset
        end
      end)

      # Send verification email for registered clients
      after_action(fn _changeset, client ->
        if client.is_registered && client.email do
          # In a real app, you would send a verification email here
          # EmailVerification.send_verification_email(client.email, client.verification_token)
          :ok
        else
          :ok
        end
      end)
    end

    # Create unregistered client for booking flow
    create :create_for_booking do
      accept([:business_id, :name, :email, :phone])

      # Validate business access
      # validate(&RivaAsh.Validations.validate_business_access/2)

      change(fn changeset, _ ->
        changeset
        |> Ash.Changeset.force_change_attribute(:is_registered, false)
        |> Ash.Changeset.force_change_attribute(:email_verified, false)
      end)

      validate(present([:name]))

      validate(fn changeset, _ ->
        email = Ash.Changeset.get_attribute(changeset, :email)
        phone = Ash.Changeset.get_attribute(changeset, :phone)

        if (is_nil(email) or email == "") and (is_nil(phone) or phone == "") do
          {:error,
           [
             %{field: :email, message: "must provide at least one of email or phone"},
             %{field: :phone, message: "must provide at least one of email or phone"}
           ]}
        else
          :ok
        end
      end)

      description("Create unregistered client during booking process")
    end

    # Register an unregistered client
    update :register do
      accept([:email, :phone, :name])
      require_atomic?(false)

      # Only allow updating unregistered clients
      validate(fn changeset, _ ->
        if Ash.Changeset.get_data(changeset, :is_registered) == true do
          {:error, field: :is_registered, message: "already registered"}
        else
          :ok
        end
      end)

      # Require email for registration
      validate(present([:email]))

      # Generate verification token
      change(fn changeset, _ ->
        token = generate_verification_token()

        changeset
        |> Ash.Changeset.force_change_attribute(:is_registered, true)
        |> Ash.Changeset.force_change_attribute(:verification_token, token)
        |> Ash.Changeset.force_change_attribute(:email_verified, false)
      end)

      # Send verification email
      after_action(fn _changeset, client ->
        # In a real app, you would send a verification email here
        # EmailVerification.send_verification_email(client.email, client.verification_token)
        :ok
      end)

      description("Convert unregistered client to registered client")
    end

    # Verify client's email
    update :verify_email do
      accept([:verification_token])
      require_atomic?(false)

      validate(fn changeset, _ ->
        if Ash.Changeset.get_data(changeset, :email_verified) == true do
          {:error, field: :email_verified, message: "already verified"}
        else
          :ok
        end
      end)

      change(fn changeset, _ ->
        token = Ash.Changeset.get_attribute(changeset, :verification_token)

        if token == changeset.data.verification_token do
          changeset
          |> Ash.Changeset.force_change_attribute(:email_verified, true)
          |> Ash.Changeset.force_change_attribute(:verification_token, nil)
        else
          Ash.Changeset.add_error(changeset, "Invalid verification token")
        end
      end)

      description("Verify client's email address using verification token")
    end

    # Find or create client for booking (handles both cases)
    create :find_or_create_for_booking do
      accept([:name, :email, :phone])

      validate(fn changeset, _ ->
        email = Ash.Changeset.get_attribute(changeset, :email)
        phone = Ash.Changeset.get_attribute(changeset, :phone)

        if (is_nil(email) or email == "") and (is_nil(phone) or phone == "") do
          {:error,
           [
             %{field: :email, message: "must provide at least one of email or phone"},
             %{field: :phone, message: "must provide at least one of email or phone"}
           ]}
        else
          :ok
        end
      end)

      # Use business_id + email as upsert key if available
      upsert?(true)
      upsert_identity(:unique_email_per_business)

      change(fn changeset, _ ->
        changeset
        |> Ash.Changeset.force_change_attribute(:is_registered, false)
        |> Ash.Changeset.force_change_attribute(:email_verified, false)
      end)

      change(fn changeset, _ ->
        # If we're updating an existing client, preserve their registration status
        if Ash.Changeset.get_attribute(changeset, :id) do
          changeset
        else
          Ash.Changeset.force_change_attributes(changeset, %{
            is_registered: false,
            email_verified: false
          })
        end
      end)

      description("Find existing client by email or create new unregistered client")
    end

    read :by_id do
      argument(:id, :uuid, allow_nil?: false)
      get?(true)
      filter(expr(id == ^arg(:id)))
    end

    read :by_email do
      argument(:email, :string, allow_nil?: false)
      filter(expr(email == ^arg(:email)))
    end

    read :by_business do
      argument(:business_id, :uuid, allow_nil?: false)
      filter(expr(business_id == ^arg(:business_id)))
    end

    read :by_business_and_email do
      argument(:business_id, :uuid, allow_nil?: false)
      argument(:email, :string, allow_nil?: false)
      filter(expr(business_id == ^arg(:business_id) and email == ^arg(:email)))
    end

    read :registered do
      filter(expr(is_registered == true))
    end

    read :unregistered do
      filter(expr(is_registered == false))
    end
  end

  attributes do
    uuid_primary_key(:id)

    attribute :business_id, :uuid do
      allow_nil?(false)
      public?(true)
      description("The business this client belongs to")
    end

    attribute :name, :string do
      allow_nil?(false)
      public?(true)
      description("The name of the client")

      constraints(
        min_length: 2,
        max_length: 100,
        trim?: true,
        allow_empty?: false
      )
    end

    attribute :email, :ci_string do
      allow_nil?(true)
      public?(true)
      description("Email address (required for registered clients)")

      constraints(
        max_length: 255,
        trim?: true,
        match: ~r/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/
      )
    end

    attribute :phone, :string do
      allow_nil?(true)
      public?(true)
      description("Contact phone number")

      constraints(
        max_length: 20,
        trim?: true,
        match: ~r/^[\d\s\-\(\)\+]+$/
      )
    end

    attribute :is_registered, :boolean do
      allow_nil?(false)
      default(false)
      public?(true)
      description("Whether the client has completed registration")
    end

    attribute :email_verified, :boolean do
      allow_nil?(false)
      default(false)
      public?(true)
      description("Whether the client's email has been verified")
    end

    attribute :verification_token, :string do
      allow_nil?(true)
      public?(false)
      description("Token used for email verification")
    end

    create_timestamp(:inserted_at)
    update_timestamp(:updated_at)
  end

  identities do
    # Email must be unique within each business
    identity(:unique_email_per_business, [:business_id, :email], eager_check_with: RivaAsh.Domain)
  end

  validations do
    # Email is required for registered clients
    validate(fn changeset, _ ->
      if Ash.Changeset.get_attribute(changeset, :is_registered) == true do
        case Ash.Changeset.get_attribute(changeset, :email) do
          nil -> {:error, field: :email, message: "is required for registered clients"}
          "" -> {:error, field: :email, message: "is required for registered clients"}
          _ -> :ok
        end
      else
        :ok
      end
    end)

    # Email format validation for registered clients
    # Enhanced email validation
    # validate(&RivaAsh.Validations.validate_email_format/2)

    # Phone validation
    # validate(&RivaAsh.Validations.validate_phone_format/2)

    # Text sanitization
    # validate(&RivaAsh.Validations.sanitize_text_input/2)

    # Business logic validation for registered clients
    validate(fn changeset, _ ->
      if Ash.Changeset.get_attribute(changeset, :is_registered) == true do
        case Ash.Changeset.get_attribute(changeset, :email) do
          email when is_binary(email) and email != "" -> :ok
          _ -> {:error, field: :email, message: "Email is required for registered clients"}
        end
      else
        :ok
      end
    end)

    # Required fields
    validate(present([:name]), message: "Name is required")
  end

  # Private helper functions

  defp generate_verification_token do
    :crypto.strong_rand_bytes(32) |> Base.url_encode64(padding: false)
  end

  relationships do
    belongs_to :business, RivaAsh.Resources.Business do
      allow_nil?(false)
      attribute_writable?(true)
      public?(true)
      description("The business this client belongs to")
    end

    has_many :reservations, RivaAsh.Resources.Reservation do
      public?(true)
      description("Reservations made by this client")
    end

    has_many :recurring_reservations, RivaAsh.Resources.RecurringReservation do
      destination_attribute(:client_id)
      public?(true)
      description("Recurring reservation patterns for this client")
    end
  end
end
