defmodule RivaAsh.Jobs.GDPRRetentionJob do
  @moduledoc """
  Background job for automated GDPR data retention compliance.

  This job should be scheduled to run daily to ensure compliance with
  GDPR data retention requirements. It performs:

  1. Cleanup of expired data based on retention policies
  2. Anonymization of data that must be kept for business purposes
  3. Generation of compliance reports
  4. Notification of any compliance issues

  Schedule this job to run daily during low-traffic hours.
  """

  use GenServer

  alias RivaAsh.GDPR.RetentionPolicy

  require Logger

  @job_name "gdpr_retention_cleanup"
  # Run at 2 AM daily
  @default_schedule "0 2 * * *"

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @impl true
  def init(opts) do
    schedule = Keyword.get(opts, :schedule, @default_schedule)

    # Schedule the first run
    schedule_next_run(schedule)

    Logger.info("GDPR Retention Job started with schedule: #{schedule}")

    {:ok, %{schedule: schedule, last_run: nil, stats: %{}}}
  end

  @impl true
  def handle_info(:run_retention_cleanup, state) do
    Logger.info("GDPR: Starting scheduled retention cleanup")

    start_time = System.monotonic_time(:millisecond)

    try do
      # Run the retention cleanup
      {:ok, results} = RetentionPolicy.run_retention_cleanup()

      # Generate compliance report
      report = RetentionPolicy.generate_retention_report()

      # Calculate execution time
      execution_time = System.monotonic_time(:millisecond) - start_time

      # Update state with results
      new_state = %{
        state
        | last_run: DateTime.utc_now(),
          stats:
            Map.merge(results, %{
              execution_time_ms: execution_time,
              report: report
            })
      }

      # Log success
      Logger.info("GDPR: Retention cleanup completed successfully",
        extra: %{
          execution_time_ms: execution_time,
          results: results
        }
      )

      # Send notification if there were any issues
      if has_compliance_issues?(results) do
        send_compliance_alert(results)
      end

      # Schedule next run
      schedule_next_run(state.schedule)

      {:noreply, new_state}
    rescue
      error ->
        Logger.error("GDPR: Retention cleanup failed",
          extra: %{
            error: inspect(error),
            stacktrace: __STACKTRACE__
          }
        )

        # Send alert about failure
        send_failure_alert(error)

        # Schedule retry in 1 hour
        Process.send_after(self(), :run_retention_cleanup, :timer.hours(1))

        {:noreply, state}
    end
  end

  @impl true
  def handle_call(:get_stats, _from, state) do
    {:reply, state.stats, state}
  end

  @impl true
  def handle_call(:run_now, _from, state) do
    # Trigger immediate run
    send(self(), :run_retention_cleanup)
    {:reply, :ok, state}
  end

  @impl true
  def handle_cast({:update_schedule, new_schedule}, state) do
    Logger.info("GDPR: Updating retention job schedule to: #{new_schedule}")

    new_state = %{state | schedule: new_schedule}
    schedule_next_run(new_schedule)

    {:noreply, new_state}
  end

  # Public API

  @doc """
  Get statistics from the last retention cleanup run.
  """
  def get_stats do
    GenServer.call(__MODULE__, :get_stats)
  end

  @doc """
  Trigger an immediate retention cleanup run.
  """
  def run_now do
    GenServer.call(__MODULE__, :run_now)
  end

  @doc """
  Update the cleanup schedule.
  """
  def update_schedule(new_schedule) do
    GenServer.cast(__MODULE__, {:update_schedule, new_schedule})
  end

  # Private helper functions

  defp schedule_next_run(_schedule) do
    # Parse cron schedule and calculate next run time
    # For simplicity, we'll run every 24 hours
    # In production, you'd use a proper cron parser

    next_run_ms = :timer.hours(24)
    Process.send_after(self(), :run_retention_cleanup, next_run_ms)

    Logger.debug("GDPR: Next retention cleanup scheduled in #{next_run_ms}ms")
  end

  defp has_compliance_issues?(results) do
    # Check if any cleanup operations failed or if there are concerning patterns
    total_processed =
      results
      |> Map.values()
      |> Enum.sum()

    # Alert if we processed more than expected (might indicate a problem)
    total_processed > 1000 or
      Map.get(results, :errors, 0) > 0
  end

  defp send_compliance_alert(results) do
    Logger.warning("GDPR: Compliance issues detected during retention cleanup", extra: results)

    # In production, this would send emails/notifications to compliance team
    # For now, we'll just log the alert

    _alert_data = %{
      timestamp: DateTime.utc_now(),
      job: @job_name,
      alert_type: "compliance_issue",
      details: results
    }

    # This could integrate with your notification system
    # send_notification(:compliance_team, :gdpr_alert, alert_data)
  end

  defp send_failure_alert(error) do
    Logger.error("GDPR: Critical failure in retention cleanup job",
      extra: %{
        error: inspect(error),
        timestamp: DateTime.utc_now()
      }
    )

    # In production, this would send immediate alerts to technical team
    _alert_data = %{
      timestamp: DateTime.utc_now(),
      job: @job_name,
      alert_type: "job_failure",
      error: inspect(error),
      severity: "critical"
    }

    # This could integrate with your alerting system (PagerDuty, etc.)
    # send_alert(:technical_team, :critical, alert_data)
  end

  @doc """
  Health check function to verify the job is running properly.
  """
  def health_check do
    try do
      stats = get_stats()

      case stats do
        %{last_run: nil} ->
          {:warning, "Job has not run yet"}

        %{last_run: last_run} ->
          hours_since_last_run = DateTime.diff(DateTime.utc_now(), last_run, :hour)

          cond do
            hours_since_last_run > 48 ->
              {:error, "Job has not run in #{hours_since_last_run} hours"}

            hours_since_last_run > 25 ->
              {:warning, "Job is overdue (#{hours_since_last_run} hours since last run)"}

            true ->
              {:ok, "Job is running normally (last run: #{hours_since_last_run} hours ago)"}
          end

        _ ->
          {:error, "Unable to get job statistics"}
      end
    rescue
      error ->
        {:error, "Health check failed: #{inspect(error)}"}
    end
  end

  @doc """
  Generate a compliance report for the retention job.
  """
  def generate_job_report do
    stats = get_stats()
    health = health_check()

    %{
      job_name: @job_name,
      schedule: "Daily at 2 AM UTC",
      health_status: health,
      last_run_stats: stats,
      report_generated_at: DateTime.utc_now(),
      compliance_notes: [
        "Job runs daily to ensure GDPR Article 5(1)(e) compliance",
        "Retention periods are defined in RivaAsh.GDPR.RetentionPolicy",
        "All cleanup operations are logged for audit purposes",
        "Failures trigger immediate alerts to technical team"
      ]
    }
  end
end
